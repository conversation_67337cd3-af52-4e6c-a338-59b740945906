import 'package:app/i18n/i18n.dart';
import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/multi_select.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/filters/date_range.dart';
import 'package:app/shared/widgets/filters/list.dart';
import 'package:app/shared/widgets/filters/numeric.dart';
import 'package:app/shared/widgets/filters/text.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FilteringFilterButton<T extends FilterCubit> extends StatelessWidget {
  const FilteringFilterButton({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<T, FilterState>(
      builder: (context, state) {
        int selectedCount = state.values.entries
            .where((e) => e.value.value != null && e.value.value != '')
            .fold<int>(0, (count, f) {
          if (state.config.advancedFilterConfig
              .map((f) => f.id)
              .contains(f.key)) {
            if (f.value.value is Map) {
              return count + (f.value.value as Map).length;
            } else {
              return count + 1;
            }
          }

          return count;
        });

        final tr = context.translator;

        return FLFilterButton.filter(
          text: tr(Labels.filter.filtering) + ' ($selectedCount)',
          onPressed: state.isUpdating
              ? null
              : () {
                  final cubit = context.read<T>();

                  FLModalBottomSheet.showMultiPage(
                    context: context,
                    builder: (context, sheet) {
                      return BlocProvider.value(
                        value: cubit,
                        child: sheet,
                      );
                    },
                    pagesBuilder: (context, controller) {
                      return [
                        FLSliverModalBottomSheetPage(
                          title: FLModalBottomSheetTitle.regular(
                            title: tr(Labels.filter.label),
                            showDivider: true,
                          ),
                          mainContentSlivers: [
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final f =
                                      state.config.advancedFilterConfig[index];

                                  return f.map<Widget>(
                                    query: (_) => const SizedBox.shrink(),
                                    list: (f) => ListFilter<T>(filter: f),
                                    dateRange: (f) =>
                                        DateRangeFilter<T>(filter: f),
                                    text: (f) => TextFilter<T>(filter: f),
                                    numeric: (f) => NumericFilter<T>(filter: f),
                                    multiSelect: (f) =>
                                        MultiSelectFilter<T>(
                                          filter: f,
                                          onTapCallbackNavigate: () {
                                            controller.switchTo(1);
                                          },
                                        ),
                                  );
                                },
                                childCount:
                                    state.config.advancedFilterConfig.length,
                              ),
                            ),
                          ],
                        ),
                        FLSliverModalBottomSheetPage(
                          title: FLModalBottomSheetTitle.regular(
                            title: tr(Labels.filter.label),
                            showDivider: true,
                          ),
                          mainContentSlivers: [
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final f =
                                      state.config.advancedFilterConfig[index];

                                  return f.map<Widget>(
                                    query: (_) => const SizedBox.shrink(),
                                    list: (_) => const SizedBox.shrink(),
                                    dateRange: (_) => const SizedBox.shrink(),
                                    text: (_) => const SizedBox.shrink(),
                                    numeric: (_) => const SizedBox.shrink(),
                                    multiSelect: (f) => _buildMultiSelectPage<T>(
                                      context: context,
                                      filter: f,
                                      state: state,
                                      controller: controller,
                                    ),
                                  );
                                },
                                childCount:
                                    state.config.advancedFilterConfig.length,
                              ),
                            ),
                          ],
                          actionBar: FLModalBottomSheetActionBar.regular(
                            text: 'Apply',
                            onPressed: () => {
                              // Save filters
                              // Navigate back to the first page
                              controller.switchTo(0),
                            },
                          ),
                        ),
                      ];
                    },
                  );
                },
        );
      },
    );
  }

  Widget _buildMultiSelectPage<U extends FilterCubit>({
    required BuildContext context,
    required MultiSelectFilterModel filter,
    required FilterState state,
    required FLModalBottomSheetController controller,
  }) {
    final cubit = context.read<U>();
    final currentValue = state.values[filter.label];
    final selectedValuesString = currentValue?.value as String? ?? '';
    final selectedValues = selectedValuesString.isEmpty
        ? <String>{}
        : selectedValuesString.split(',').toSet();

    return Padding(
      padding: const EdgeInsets.only(
        top: FLSpacings.sm,
        bottom: FLSpacings.xlg,
      ),
      child: Column(
        children: filter.values.map((option) {
          final isSelected = selectedValues.contains(option.value);
          return ListTile(
            title: Text(tr(context, option.label)),
            trailing: Checkbox(
              value: isSelected,
              onChanged: (checked) {
                final newSelectedValues = Set<String>.from(selectedValues);
                if (checked ?? false) {
                  newSelectedValues.add(option.value);
                } else {
                  newSelectedValues.remove(option.value);
                }

                final newValue = newSelectedValues.isEmpty
                    ? ''
                    : newSelectedValues.join(',');

                cubit.add(
                  id: filter.label,
                  value: FilterValueModel(
                    label: filter.label,
                    value: newValue,
                  ),
                );
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
