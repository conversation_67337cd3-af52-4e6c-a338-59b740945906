import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/heading.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MultiSelectFilter<T extends FilterCubit> extends StatelessWidget {
  final MultiSelectFilterModel filter;
  final VoidCallback? onTap;
  final VoidCallback? onTapCallbackNavigate;

  const MultiSelectFilter({
    Key? key,
    required this.filter,
    this.onTap,
    this.onTapCallbackNavigate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<T, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.label] !=
            previousState.values[filter.label];
      },
      builder: (context, state) {
        return _MultiSelectFilterText(
          filter: filter,
          state: state,
          onTap: onTapCallbackNavigate,
        );
      },
    );
  }
}

class _MultiSelectFilterText extends StatelessWidget {
  final MultiSelectFilterModel filter;
  final FilterState state;
  final VoidCallback? onTap;

  const _MultiSelectFilterText({
    Key? key,
    required this.filter,
    required this.state,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentValue = state.values[filter.label];
        final selectedValuesString = currentValue?.value as String? ?? '';
        final selectedValues = selectedValuesString.isEmpty
            ? <String>{}
            : selectedValuesString.split(',').toSet();

        String displayText;
        if (selectedValues.isEmpty) {
          displayText = tr(context, filter.label);
        } else {
          final selectedLabels = filter.values
              .where((item) => selectedValues.contains(item.value))
              .map((item) => tr(context, item.label))
              .toList();
          displayText = selectedLabels.join(', ');
        }

    return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FilterHeading(
              label: tr(context, filter.label),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GestureDetector(
                onTap: onTap,
                child: FLTextField(
                  enabled: false,
                  decoration: FLTextFieldDecoration(
                    hintText: displayText,
                  ),
                ),
              ),
            ),
            const Gap(4),
            const Divider(),
          ],
        );
  }
}

class _MultiSelectFilterPage extends StatefulWidget {
  final String title;
  final List<FilterValueModel<String>> options;
  final Set<String> initialSelected;
  final ValueChanged<Set<String>> onSelectionChanged;

  const _MultiSelectFilterPage({
    Key? key,
    required this.title,
    required this.options,
    required this.initialSelected,
    required this.onSelectionChanged,
  }) : super(key: key);

  @override
  State<_MultiSelectFilterPage> createState() => _MultiSelectFilterPageState();
}

class _MultiSelectFilterPageState extends State<_MultiSelectFilterPage> {
  late Set<String> selected;

  @override
  void initState() {
    super.initState();
    selected = Set<String>.from(widget.initialSelected);
  }

  void _onItemTap(String id) {
    setState(() {
      if (selected.contains(id)) {
        selected.remove(id);
      } else {
        selected.add(id);
      }
      widget.onSelectionChanged(selected);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              Expanded(
                child: Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: widget.options.length,
            itemBuilder: (context, index) {
              final option = widget.options[index];
              final isSelected = selected.contains(option.value);
              return ListTile(
                title: Text(option.label),
                trailing: Checkbox(
                  value: isSelected,
                  onChanged: (checked) {
                    _onItemTap(option.value);
                  },
                ),
                // Remove onTap from ListTile to prevent toggling by tapping the row
              );
            },
          ),
        ),
      ],
    );
  }
}