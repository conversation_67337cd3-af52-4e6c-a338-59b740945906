import 'package:app/i18n/i18n.dart';
import 'package:app/receivings_module/cubits/filter/cubits.dart';
import 'package:app/receivings_module/cubits/provisional_booking_total/cubit.dart';
import 'package:app/receivings_module/models/filter/non_po_receivings_config.dart';
import 'package:app/receivings_module/models/filter/orders_config.dart';
import 'package:app/receivings_module/models/filter/provisional_bookings_config.dart';
import 'package:app/receivings_module/widgets/process_receivings/app_bar.dart';
import 'package:app/receivings_module/widgets/process_receivings/non_po_receivings/non_po_receivings_tab.dart';
import 'package:app/receivings_module/widgets/process_receivings/orders/orders_tab.dart';
import 'package:app/receivings_module/widgets/process_receivings/provisional_bookings/provisional_booking_tab.dart';
import 'package:app/shared/cubits/api_connectivity/cubit.dart';
import 'package:app/shared/helpers/dialogs.dart';
import 'package:app/shared/repositories/receivings.dart';
import 'package:app/shared/screens/app_menu.dart';
import 'package:app/shared/screens/webshop_cost_center_lookup.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/widgets/default_tab_controller_listener.dart';
import 'package:app/shared/widgets/wrapper/api_connectivity_listener.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProcessReceivingsScreen extends StatefulWidget {
  const ProcessReceivingsScreen({Key? key}) : super(key: key);

  static MaterialPageRoute<void> route() {
    return MaterialPageRoute<void>(
      settings: const RouteSettings(name: '/process_receivings'),
      builder: (_) => const ProcessReceivingsScreen(),
    );
  }

  @override
  State<ProcessReceivingsScreen> createState() =>
      _ProcessReceivingsScreenState();
}

class _ProcessReceivingsScreenState extends State<ProcessReceivingsScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final tr = context.translator;

    final isProvisionalBookingEnabled = context
        .read<PreferencesCacheService>()
        .getField(PreferencesField.division)
        .isProvisionalBookingEnabled;

    return V3ThemeWrapper(
      child: MultiBlocProvider(
        providers: [
          BlocProvider<ProcessReceivingsOrdersFilterCubit>(
            create: (context) => ProcessReceivingsOrdersFilterCubit(
              ProcessReceivingOrdersFilterConfig(
                valueInputDelegates: {
                  RECEIVING_ORDERS_COST_CENTER_NAME_FID: (_) =>
                      _costCenterLookup(context),
                  RECEIVING_ORDERS_REQUESTED_BY_COST_CENTER_NAME_FID: (_) =>
                      _costCenterLookup(context),
                },
              ),
            ),
          ),
          BlocProvider<ProcessReceivingsNonPoFilterCubit>(
            create: (_) => ProcessReceivingsNonPoFilterCubit(
              ProcessReceivingsNonPoFilterConfig(
                valueInputDelegates: {
                  NON_PO_RECEIVINGS_COST_CENTER_NAME_FID: (_) =>
                      _costCenterLookup(
                        context,
                      ),
                },
              ),
            ),
          ),
          BlocProvider<ProvisionalBookingsFilterCubit>(
            create: (_) => ProvisionalBookingsFilterCubit(
              ProvisionalBookingsFilterConfig(
                valueInputDelegates: {
                  PROVISIONAL_BOOKINGS_COST_CENTER_NAME_FID: (_) =>
                      _costCenterLookup(context),
                  PROVISIONAL_BOOKINGS_REQUESTED_BY_COST_CENTER_NAME_FID: (_) =>
                      _costCenterLookup(context),
                },
              ),
            ),
          ),
          BlocProvider<ProvisionalBookingTotalCubit>(
            create: (context) => ProvisionalBookingTotalCubit(
              context.read<ReceivingsRepository>(),
              isOnline: context.read<ApiConnectivityCubit>().state,
              isProvisionalBookingsEnabled: isProvisionalBookingEnabled,
            )..load(),
          ),
        ],
        child: ApiConnectivityListener(
          onConnectionStateChanged: (context, networkState) =>
              context.read<ProvisionalBookingTotalCubit>().updateNetworkState(
                    networkState,
                  ),
          child: DefaultTabController(
            length: isProvisionalBookingEnabled ? 3 : 2,
            child: DefaultTabControllerListener(
              onTabChanged: (int index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              child: Builder(
                builder: (context) {
                  return Scaffold(
                    appBar: ProcessReceivingsAppBar(
                      entity: switch (_selectedIndex) {
                        0 => ProcessReceivingsAppBarEntity.orders,
                        1 => ProcessReceivingsAppBarEntity.non_po_receivings,
                        2 => ProcessReceivingsAppBarEntity.provisional_bookings,
                        _ => ProcessReceivingsAppBarEntity.orders,
                      },
                    ),
                    drawer: const AppMenu(),
                    body: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        BlocBuilder<ProvisionalBookingTotalCubit,
                            ProvisionalBookingTotalState>(
                          buildWhen: (previous, current) =>
                              previous.entity != current.entity,
                          builder: (context, state) {
                            return FLTabBar(
                              isScrollable: true,
                              controller: DefaultTabController.of(context),
                              tabs: [
                                FLTab(
                                  label: tr(
                                    Labels.process_receivings.orders.title,
                                  ),
                                ),
                                FLTab(
                                  label: tr(
                                    Labels.process_receivings.non_po_receivings
                                        .title,
                                  ),
                                ),
                                if (isProvisionalBookingEnabled)
                                  FLTab(
                                    label: tr(
                                      Labels.process_receivings
                                          .provisional_booking.title,
                                      arguments: {
                                        'count':
                                            '${state.entity?.canBeBookedCount ?? 0}',
                                      },
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                        Expanded(
                          child: TabBarView(
                            controller: DefaultTabController.of(context),
                            children: [
                              const ProcessReceivingsOrdersTab(),
                              const ProcessReceivingsNonPOReceivingsTab(),
                              if (isProvisionalBookingEnabled)
                                const ProvisionalBookingsTab(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<String?> _orderTypeBottomSheet(BuildContext context) async {
    final tr = context.translator;

    final List<Map<String, String>> options = [
      {
        'value': RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL,
        'label': tr(Labels.receivings.orders.filter.order_type.all),
      },
      {
        'value': RECEIVING_ORDERS_ORDER_TYPE_MATCH_EXACT,
        'label': tr(Labels.receivings.orders.filter.order_type.exact_match),
      },
      {
        'value': RECEIVING_ORDERS_ORDER_TYPE_MATCH_PARTIAL,
        'label': tr(Labels.receivings.orders.filter.order_type.partial),
      },
      {
        'value': RECEIVING_ORDERS_ORDER_TYPE_MATCH_RELATED,
        'label': tr(Labels.receivings.orders.filter.order_type.related),
      },
    ];

    final title = tr(Labels.receivings.orders.filter.order_type.label);

    final result = await Dialogs.showCheckboxBottomSheet(
      context,
      title,
      options,
    );

    if (result == null) {
      return null;
    }

    final selectedLabels = options
        .where((option) => result.contains(option['value']!))
        .map((option) => option['label']!)
        .toList();
    // return result value
    return selectedLabels.join(', ');
  }

  Future<String?> _costCenterLookup(BuildContext context) async {
    final result = await Navigator.of(context).push(
      WebshopCostCenterLookupScreen.route(
        filterByDivisionId: context
            .read<PreferencesCacheService>()
            .getField(PreferencesField.division)
            .id,
      ),
    );

    if (result == null) {
      return null;
    }

    return result.costCenter;
  }
}
